package model

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ProductVariant struct {
	ID    string `json:"id"`
	Title string `json:"title,omitempty"`
	Price string `json:"price,omitempty"`
}

type ProductShopify struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	ShopifyID string             `json:"shopify_id" bson:"shopify_id"`
	Shop      string             `json:"shop" bson:"shop"`
	Title     string             `json:"title,omitempty" bson:"title,omitempty"`
	Handle    string             `json:"handle,omitempty" bson:"handle,omitempty"`
	Status    string             `json:"status,omitempty" bson:"status,omitempty"`
	Variants  []ProductVariant   `json:"variants,omitempty" bson:"variants,omitempty"`
}

type Variant struct {
	ID    string `json:"id" bson:"id"`
	Title string `json:"title,omitempty" bson:"title,omitempty"`
	Price string `json:"price,omitempty" bson:"price,omitempty"`
}

type ProductImage struct {
	Src string `json:"src"`
}

type ProductMedia struct {
	ID string `json:"id" bson:"id"`
}

type Product struct {
	ID       string         `json:"id" bson:"id"`
	Title    string         `json:"title,omitempty" bson:"title,omitempty"`
	Images   []ProductImage `json:"images,omitempty" bson:"images,omitempty"`
	Media    *ProductMedia  `json:"media,omitempty" bson:"media,omitempty"`
	Status   string         `json:"status,omitempty" bson:"status,omitempty"`
	Variants []Variant      `json:"variants" bson:"variants"`
	Handle   string         `json:"handle,omitempty" bson:"handle,omitempty"`
}

type CountdownAppearance struct {
	Template string `json:"template" bson:"template"`
	Position string `json:"position" bson:"position"`
}

type SelectorType struct {
	Wrapper        string `json:"wrapper,omitempty" bson:"wrapper,omitempty"`
	ProgressBar    string `json:"progress_bar,omitempty" bson:"progress_bar,omitempty"`
	RewardFreeGift string `json:"reward_free_gift,omitempty" bson:"reward_free_gift,omitempty"`
	RewardDiscount string `json:"reward_discount,omitempty" bson:"reward_discount,omitempty"`
	Footer         struct {
		Wrapper string   `json:"wrapper,omitempty" bson:"wrapper,omitempty"`
		Hide    []string `json:"hide,omitempty" bson:"hide,omitempty"`
	}
	DetectChange        string `json:"detect_change,omitempty" bson:"detect_change,omitempty"`
	NotCalcPositionGift bool   `json:"not_calc_position_gift,omitempty" bson:"not_calc_position_gift,omitempty"`
	TimeoutRender       int64  `json:"timeout_render,omitempty" bson:"timeout_render,omitempty"`
	CartItems           *struct {
		Wrapper string `json:"wrapper,omitempty" bson:"wrapper,omitempty"`
		Item    string `json:"item,omitempty" bson:"item,omitempty"`
	} `json:"cart_items,omitempty" bson:"cart_items,omitempty"`
	FormSubmit     string `json:"form_submit,omitempty" bson:"form_submit,omitempty"`
	DetectDisplay  string `json:"detect_display,omitempty" bson:"detect_display,omitempty"`
	CheckoutButton string `json:"checkout_button,omitempty" bson:"checkout_button,omitempty"`
}

type ProductBlockSelector struct {
	CartPage   *SelectorType `json:"cart_page,omitempty" bson:"cart_page,omitempty"`
	CartDrawer *SelectorType `json:"cart_drawer,omitempty" bson:"cart_drawer,omitempty"`
}

type ProductSizeChart struct {
	Handle    string `json:"handle,omitempty" bson:"handle,omitempty"`
	Title     string `json:"title,omitempty" bson:"title,omitempty"`
	Image     string `json:"image,omitempty" bson:"image,omitempty"`
	ProductId string `json:"product_id" bson:"product_id"`
}

type AppearanceSize struct {
	Desktop int64 `json:"desktop,omitempty" bson:"desktop,omitempty"`
	Mobile  int64 `json:"mobile,omitempty" bson:"mobile,omitempty"`
}

type AppearanceColor struct {
	Background       string `json:"background,omitempty" bson:"background,omitempty"`
	Text             string `json:"text,omitempty" bson:"text,omitempty"`
	ButtonColor      string `json:"button_color,omitempty" bson:"button_color,omitempty"`
	ButtonTextColor  string `json:"button_text,omitempty" bson:"button_text,omitempty"`
	ButtonLinkColor  string `json:"button_link,omitempty" bson:"button_link,omitempty"`
	Checkbox         string `json:"checkbox,omitempty" bson:"checkbox,omitempty"`
	Warning          string `json:"warning,omitempty" bson:"warning,omitempty"`
	ButtonBackground string `json:"button_background,omitempty" bson:"button_background,omitempty"`
	Border           string `json:"border,omitempty" bson:"border,omitempty"`
	// Spending goal tracker
	HighlightColor string `json:"highlight_color,omitempty" bson:"highlight_color,omitempty"`
	// Feature icon
	IconColor string `json:"icon_color,omitempty" bson:"icon_color,omitempty"`
	// Insurance addon
	Price  string `json:"price,omitempty" bson:"price,omitempty"`
	Toggle string `json:"toggle,omitempty" bson:"toggle,omitempty"`
}

type Appearance struct {
	Template  string          `json:"template,omitempty" bson:"template,omitempty"`
	Position  string          `json:"position,omitempty" bson:"position,omitempty"` // default position on desktop
	Animation string          `json:"animation,omitempty" bson:"animation,omitempty"`
	Color     AppearanceColor `json:"color,omitempty" bson:"color,omitempty"`
	Size      AppearanceSize  `json:"size,omitempty" bson:"size,omitempty"`
	ShowOn    []string        `json:"show_on,omitempty" bson:"show_on,omitempty"`
	// Spending goal tracker
	PositionMobile string `json:"position_mobile,omitempty" bson:"position_mobile,omitempty"` // position on mobile
}

type ProductLabelAndBadge struct {
	ID          primitive.ObjectID  `json:"id" bson:"_id,omitempty"`
	Name        string              `json:"name,omitempty" bson:"name"`
	Category    string              `json:"category,omitempty" bson:"category"`
	Status      bool                `json:"status" bson:"status"`
	Products    *[]ProductSizeChart `json:"products" bson:"products"`
	Thumbnail   string              `json:"thumbnail,omitempty" bson:"thumbnail,omitempty"`
	Position    string              `json:"position,omitempty" bson:"position,omitempty"`
	Animation   string              `json:"animation,omitempty" bson:"animation,omitempty"`
	SizeDesktop int64               `json:"size_desktop,omitempty" bson:"size_desktop,omitempty"`
	SizeMobile  int64               `json:"size_mobile,omitempty" bson:"size_mobile,omitempty"`
}

type MessageScrollingTextBanner struct {
	ID      primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Message string             `json:"message" bson:"message"`
	Link    string             `json:"link" bson:"link"`
	Icon    string             `json:"icon" bson:"icon"`
}

var SpendingGoalPlacement = map[string]string{
	"index":            "Homepage",
	"product":          "Product pages",
	"list-collections": "Collections list page",
	"collection":       "Collections page",
	"cart":             "Cart page",
}

type SpendingGoalDiscount struct {
	Code                  string `json:"code,omitempty" bson:"code"`
	Value                 int64  `json:"value" bson:"value"`
	Type                  string `json:"type" bson:"type"`
	CombineWithOther      bool   `json:"-" bson:"-"`
	MinimumPurchaseAmount int64  `json:"-" bson:"-"`
	ShopifyID             string `json:"-" bson:"shopify_id"`
}

type MessageSpendingGoal struct {
	Initial  string `json:"initial" bson:"initial"`
	Progress string `json:"progress" bson:"progress"`
	Reached  string `json:"reached" bson:"reached"`
}

type PlacementSpendingGoal struct {
	ShowOnAllPages bool              `json:"show_on_all_pages" bson:"show_on_all_pages"`
	Placement      map[string]string `json:"placement" bson:"placement"`
}

type LimitSetting struct {
	MinValue        int64  `json:"min_value" bson:"min_value"`
	MaxValue        int64  `json:"max_value" bson:"max_value"`
	Unit            string `json:"unit,omitempty" bson:"unit,omitempty"`
	MinMessageReach string `json:"min_message_reach,omitempty" bson:"min_message_reach,omitempty"`
	MaxMessageReach string `json:"max_message_reach,omitempty" bson:"max_message_reach,omitempty"`
}

type ItemLimitSetting struct {
	Active  bool         `json:"active" bson:"active"`
	Setting LimitSetting `json:"setting" bson:"setting"`
}

type OrderLimitSetting struct {
	ProductQuantity *ItemLimitSetting `json:"product_quantity,omitempty" bson:"product_quantity,omitempty"`
	OrderValue      *ItemLimitSetting `json:"order_value,omitempty" bson:"order_value,omitempty"`
	OrderWeight     *ItemLimitSetting `json:"order_weight,omitempty" bson:"order_weight,omitempty"`
}

type ProductItemSetting struct {
	ProductID string `json:"product_id,omitempty" bson:"product_id,omitempty"`
	Title     string `json:"title,omitempty" bson:"title,omitempty"`
	Image     string `json:"image,omitempty" bson:"image,omitempty"`
	Price     string `json:"price,omitempty" bson:"price,omitempty"`
	Unit      string `json:"unit,omitempty" bson:"unit,omitempty"`
	Handle    string `json:"handle,omitempty" bson:"handle,omitempty"`
	Min       int64  `json:"min,omitempty" bson:"min,omitempty"`
	Max       int64  `json:"max,omitempty" bson:"max,omitempty"`
}

type MessageProductLimit struct {
	MinMessageReach string `json:"min_message_reach,omitempty" bson:"min_message_reach,omitempty"`
	MaxMessageReach string `json:"max_message_reach,omitempty" bson:"max_message_reach,omitempty"`
}

type ProductLimitSetting struct {
	Products *[]ProductItemSetting `json:"products,omitempty" bson:"products,omitempty"`
	Message  *MessageProductLimit  `json:"message,omitempty" bson:"message,omitempty"`
}

type FeatureIcon struct {
	Heading string             `json:"heading,omitempty" bson:"heading,omitempty"`
	Items   *[]FeatureIconItem `json:"items,omitempty" bson:"items,omitempty"`
}

type FeatureIconItem struct {
	ID          primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Icon        string             `json:"icon" bson:"icon"`
	Title       string             `json:"title" bson:"title"`
	Description string             `json:"description" bson:"description"`
	Link        string             `json:"link" bson:"link"`
}

type ProductBlock struct {
	ID              primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Shop            string             `json:"shop,omitempty" bson:"shop"`
	IsActive        bool               `json:"is_active" bson:"is_active"`
	DescriptionHtml string             `json:"description_html,omitempty" bson:"description_html"`
	Code            string             `json:"code" bson:"code"`
	Badges          *[]string          `json:"badges,omitempty" bson:"badges,omitempty"`
	Heading         string             `json:"heading" bson:"heading"`
	StyleCss        string             `json:"style_css,omitempty" bson:"style_css,omitempty"`
	Template        string             `json:"template,omitempty" bson:"template"`
	CreatedBy       string             `json:"-" bson:"created_by"`
	CreatedAt       time.Time          `json:"-" bson:"created_at"`
	UpdateAt        time.Time          `json:"-" bson:"updated_at"`
	UpdateBy        string             `json:"-" bson:"updated_by"`
	// countdown timer bar
	Timer            int64  `json:"timer" bson:"timer"`
	OnItEndAction    string `json:"on_it_end_action,omitempty" bson:"on_it_end_action"`
	AnnouncementText string `json:"announcement_text,omitempty" bson:"announcement_text"`
	Position         string `json:"position,omitempty" bson:"position"`

	// selectors
	Selectors *ProductBlockSelector `json:"selectors,omitempty" bson:"selectors,omitempty"`

	// Stock coundown
	WhenItShowAction  string `json:"when_it_show_action,omitempty" bson:"when_it_show_action,omitempty"`
	QuantityCondition int64  `json:"quantity_condition" bson:"quantity_condition"`

	// free shipping bar
	OrderValue     int64  `json:"order_value" bson:"order_value"`
	TextBefore     string `json:"text_before,omitempty" bson:"text_before,omitempty"`
	TextInProgress string `json:"text_in_progress,omitempty" bson:"text_in_progress,omitempty"`
	TextGoal       string `json:"text_goal,omitempty" bson:"text_goal,omitempty"`

	// Sales popup
	OrderStatus    *[]string `json:"order_status,omitempty" bson:"order_status,omitempty"`
	SalesPopupText string    `json:"sales_popup_text,omitempty" bson:"sales_popup_text,omitempty"`
	Placement      string    `json:"placement,omitempty" bson:"placement,omitempty"`
	Timing         *struct {
		First    int64 `json:"first" bson:"first"`
		Duration int64 `json:"duration" bson:"duration"`
		Delay    int64 `json:"delay" bson:"delay"`
	} `json:"timing,omitempty" bson:"timing,omitempty"`
	PositionSalePopup *struct {
		ShowDesktop bool   `json:"show_desktop" bson:"show_desktop"`
		ShowMobile  bool   `json:"show_mobile" bson:"show_mobile"`
		Desktop     string `json:"desktop" bson:"desktop"`
		Mobile      string `json:"mobile" bson:"mobile"`
	} `json:"position_sale_popup,omitempty" bson:"position_sale_popup,omitempty"`
	SpecificPages   *[]string `json:"specific_pages,omitempty" bson:"specific_pages,omitempty"`
	BackgroundColor string    `json:"bgColor,omitempty" bson:"bgColor,omitempty"`
	TextColor       string    `json:"textColor,omitempty" bson:"textColor,omitempty"`
	OrderCreatedAt  int64     `json:"order_created_at" bson:"order_created_at"`

	// Cookie banner
	ConfirmationText string `json:"confirmation_text,omitempty" bson:"confirmation_text,omitempty"`
	ButtonText       string `json:"button_text,omitempty" bson:"button_text,omitempty"`
	Privacy          bool   `json:"privacy" bson:"privacy"`
	PrivacyLabel     string `json:"privacy_label,omitempty" bson:"privacy_label,omitempty"`
	PrivacyLink      string `json:"privacy_link" bson:"privacy_link"`
	CloseButton      bool   `json:"close_button" bson:"close_button"`
	ShowBanner       string `json:"show_banner,omitempty" bson:"show_banner,omitempty"`

	// Terms and condition
	TermConditionText string `json:"term_condition_text,omitempty" bson:"term_condition_text,omitempty"`
	AlertText         string `json:"alert_text,omitempty" bson:"alert_text,omitempty"`

	// Add to animator
	Animation string `json:"animation" bson:"animation"`

	// Size chart
	SizeChartPosition []string `json:"size_chart_position" bson:"size_chart_position"`
	SizeChartText     string   `json:"size_chart_text" bson:"size_chart_text"`
	SizeChartList     *[]struct {
		ID              primitive.ObjectID  `json:"_id,omitempty" bson:"_id,omitempty"`
		Name            string              `json:"name" bson:"name"`
		Category        string              `json:"category" bson:"category"`
		Status          bool                `json:"status" bson:"status"`
		Products        *[]ProductSizeChart `json:"products" bson:"products"`
		DescriptionHtml string              `json:"description_html" bson:"description_html"`
	} `json:"size_chart_list" bson:"size_chart_list"`

	// Scroll to top button
	Badge string `json:"badge,omitempty" bson:"badge,omitempty"`
	Icon  string `json:"icon,omitempty" bson:"icon,omitempty"`
	Style string `json:"style,omitempty" bson:"style,omitempty"`

	// Social media buttons
	PositionMediaButtons struct {
		ShowDesktop bool   `json:"show_desktop" bson:"show_desktop"`
		ShowMobile  bool   `json:"show_mobile" bson:"show_mobile"`
		Desktop     string `json:"desktop" bson:"desktop"`
		Mobile      string `json:"mobile" bson:"mobile"`
	} `json:"position_media_buttons,omitempty" bson:"position_media_buttons,omitempty"`

	Links []struct {
		Key  string `json:"key" bson:"key"`
		Link string `json:"link" bson:"link"`
	} `json:"links,omitempty" bson:"links,omitempty"`

	// content protection
	Disable   []string `json:"disable" bson:"disable"`
	ApplyFor  string   `json:"apply_for" bson:"apply_for"`
	Trademark bool     `json:"trademark" bson:"trademark"`

	// product labels and badges
	ProductLabelsAndBadges *[]ProductLabelAndBadge `json:"product_labels,omitempty" bson:"product_labels,omitempty"`

	// Product tabs && Accordion
	ProductTabTitle                string `json:"product_tab_title" bson:"product_tab_title"`                       // tab title
	ProductTabHeadingSelector      string `json:"product_tab_heading_selector" bson:"product_tab_heading_selector"` // tab heading selector: h1-h5 hoặc null
	ProductTabDisplay              string `json:"product_tab_display" bson:"product_tab_display"`                   // horizontal, accordion
	ProductTabHorizontalAutoSwitch bool   `json:"product_tab_horizontal_auto_switch" bson:"product_tab_horizontal_auto_switch"`
	ProductTabAccordionStyle       string `json:"product_tab_accordion_style" bson:"product_tab_accordion_style"` // all_closed, all_opened, first_tab_opened

	// Appearance
	Appearance *Appearance `json:"appearance,omitempty" bson:"appearance,omitempty"`

	// scrolling_text_banner
	Messages            []MessageScrollingTextBanner `json:"messages" bson:"messages"`
	ScrollingTextBanner bool                         `json:"scrolling_text_banner" bson:"scrolling_text_banner"`
	ScrollingSpeed      int64                        `json:"scrolling_speed" bson:"scrolling_speed"`
	PauseOnMouseover    bool                         `json:"pause_on_mouseover" bson:"pause_on_mouseover"`

	// spending goal tracker
	Goal                     int                   `json:"goal" bson:"goal"`
	Discount                 SpendingGoalDiscount  `json:"discount" bson:"discount"`
	MessageSpendingGoal      MessageSpendingGoal   `json:"message_spending_goal" bson:"message_spending_goal"`
	ShowOnAllPages           bool                  `json:"show_on_all_pages" bson:"show_on_all_pages"`
	PlacementSpendingGoal    PlacementSpendingGoal `json:"placement_spending_goal" bson:"placement_spending_goal"`
	CombineWithOtherDiscount bool                  `json:"combine_with_other_discount" bson:"combine_with_other_discount"`

	// orders limit
	OrderLimitSetting *OrderLimitSetting `json:"order_limit_setting,omitempty" bson:"order_limit_setting,omitempty"`

	// products limit
	ProductLimitSetting *ProductLimitSetting `json:"product_limit_setting,omitempty" bson:"product_limit_setting,omitempty"`

	// feature icons
	FeatureIcon *FeatureIcon `json:"feature_icon_setting,omitempty" bson:"feature_icon_setting,omitempty"`

	// comparision slider
	ComparisonSlider *ComparisonSlider `json:"comparison_slider_setting,omitempty" bson:"comparison_slider_setting,omitempty"`

	// Default setting
	Default *ProductBlock `json:"default,omitempty"`
}

type ProductBlockUpdate struct {
	Code            string    `bson:"code,omitempty" json:"code,omitempty"`
	Heading         string    `bson:"heading" json:"heading"`
	DescriptionHtml string    `bson:"description_html,omitempty" json:"description_html,omitempty"`
	IsActive        bool      `bson:"is_active" json:"is_active"`
	Badges          *[]string `json:"badges,omitempty" bson:"badges,omitempty"`
	Template        string    `json:"template,omitempty" bson:"template,omitempty"`
	UpdateAt        time.Time `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	UpdateBy        string    `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	// countdown timer bar
	Timer            int64  `json:"timer" bson:"timer"`
	OnItEndAction    string `json:"on_it_end_action" bson:"on_it_end_action"`
	AnnouncementText string `json:"announcement_text" bson:"announcement_text"`
	Position         string `json:"position" bson:"position"`

	// stock countdown
	QuantityCondition int64  `json:"quantity_condition" bson:"quantity_condition"`
	WhenItShowAction  string `json:"when_it_show_action" bson:"when_it_show_action"`

	// free shipping bar
	OrderValue     int64  `json:"order_value" bson:"order_value"`
	TextBefore     string `json:"text_before,omitempty" bson:"text_before,omitempty"`
	TextInProgress string `json:"text_in_progress,omitempty" bson:"text_in_progress,omitempty"`
	TextGoal       string `json:"text_goal,omitempty" bson:"text_goal,omitempty"`

	// Sales popup
	OrderStatus    *[]string `json:"order_status,omitempty" bson:"order_status,omitempty"`
	SalesPopupText string    `json:"sales_popup_text,omitempty" bson:"sales_popup_text,omitempty"`
	Placement      string    `json:"placement,omitempty" bson:"placement,omitempty"`
	Timing         *struct {
		First    int64 `json:"first" bson:"first"`
		Duration int64 `json:"duration" bson:"duration"`
		Delay    int64 `json:"delay" bson:"delay"`
	} `json:"timing,omitempty" bson:"timing,omitempty"`
	PositionSalePopup *struct {
		ShowDesktop bool   `json:"show_desktop" bson:"show_desktop"`
		ShowMobile  bool   `json:"show_mobile" bson:"show_mobile"`
		Desktop     string `json:"desktop" bson:"desktop"`
		Mobile      string `json:"mobile" bson:"mobile"`
	} `json:"position_sale_popup,omitempty" bson:"position_sale_popup,omitempty"`
	SpecificPages   *[]string `json:"specific_pages,omitempty" bson:"specific_pages,omitempty"`
	BackgroundColor string    `json:"bgColor,omitempty" bson:"bgColor,omitempty"`
	TextColor       string    `json:"textColor,omitempty" bson:"textColor,omitempty"`
	OrderCreatedAt  int64     `json:"order_created_at" bson:"order_created_at"`

	// Cookie banner
	ConfirmationText string `json:"confirmation_text,omitempty" bson:"confirmation_text,omitempty"`
	ButtonText       string `json:"button_text,omitempty" bson:"button_text,omitempty"`
	Privacy          bool   `json:"privacy" bson:"privacy"`
	PrivacyLabel     string `json:"privacy_label,omitempty" bson:"privacy_label,omitempty"`
	PrivacyLink      string `json:"privacy_link,omitempty" bson:"privacy_link,omitempty"`
	CloseButton      bool   `json:"close_button" bson:"close_button"`
	ShowBanner       string `json:"show_banner,omitempty" bson:"show_banner,omitempty"`

	// Terms and condition
	TermConditionText string `json:"term_condition_text,omitempty" bson:"term_condition_text,omitempty"`
	AlertText         string `json:"alert_text,omitempty" bson:"alert_text,omitempty"`

	// Size chart
	SizeChartPosition []string `json:"size_chart_position" bson:"size_chart_position"`
	SizeChartText     string   `json:"size_chart_text" bson:"size_chart_text"`
	SizeChartList     []struct {
		ID              primitive.ObjectID  `json:"_id,omitempty" bson:"_id,omitempty"`
		Name            string              `json:"name" bson:"name"`
		Category        string              `json:"category" bson:"category"`
		Status          bool                `json:"status" bson:"status"`
		Products        *[]ProductSizeChart `json:"products" bson:"products"`
		DescriptionHtml string              `json:"description_html" bson:"description_html"`
	} `json:"size_chart_list" bson:"size_chart_list"`
	Products []ProductSizeChart `json:"products" bson:"products"`

	// Scroll to top button
	Badge string `json:"badge,omitempty" bson:"badge,omitempty"`
	Icon  string `json:"icon,omitempty" bson:"icon,omitempty"`
	Style string `json:"style,omitempty" bson:"style,omitempty"`

	// Social media buttons
	PositionMediaButtons struct {
		ShowDesktop bool   `json:"show_desktop" bson:"show_desktop"`
		ShowMobile  bool   `json:"show_mobile" bson:"show_mobile"`
		Desktop     string `json:"desktop" bson:"desktop"`
		Mobile      string `json:"mobile" bson:"mobile"`
	} `json:"position_media_buttons,omitempty" bson:"position_media_buttons,omitempty"`

	Links []struct {
		Key  string `json:"key" bson:"key"`
		Link string `json:"link" bson:"link"`
	} `json:"links,omitempty" bson:"links,omitempty"`

	// content protection
	Disable  []string `json:"disable" bson:"disable"`
	ApplyFor string   `json:"apply_for" bson:"apply_for"`

	// Add to animator
	Animation string `json:"animation" bson:"animation"`

	// product labels and badges
	ProductLabelsAndBadges *[]ProductLabelAndBadge `json:"product_labels,omitempty" bson:"product_labels,omitempty"`

	// Product tabs && Accordion
	ProductTabTitle                string `json:"product_tab_title" bson:"product_tab_title"`                       // tab title
	ProductTabHeadingSelector      string `json:"product_tab_heading_selector" bson:"product_tab_heading_selector"` // tab heading selector: h1-h5 hoặc null
	ProductTabDisplay              string `json:"product_tab_display" bson:"product_tab_display"`                   // horizontal, accordion
	ProductTabHorizontalAutoSwitch bool   `json:"product_tab_horizontal_auto_switch" bson:"product_tab_horizontal_auto_switch"`
	ProductTabAccordionStyle       string `json:"product_tab_accordion_style" bson:"product_tab_accordion_style"` // all_closed, all_opened, first_tab_opened

	// Appearance
	Appearance *Appearance `json:"appearance,omitempty" bson:"appearance,omitempty"`

	// scrolling_text_banner
	Messages            []MessageScrollingTextBanner `json:"messages" bson:"messages"`
	ScrollingTextBanner bool                         `json:"scrolling_text_banner" bson:"scrolling_text_banner"`
	ScrollingSpeed      int64                        `json:"scrolling_speed" bson:"scrolling_speed"`
	PauseOnMouseover    bool                         `json:"pause_on_mouseover" bson:"pause_on_mouseover"`

	// spending goal tracker
	Goal                     int                   `json:"goal" bson:"goal"`
	Discount                 SpendingGoalDiscount  `json:"discount" bson:"discount"`
	MessageSpendingGoal      MessageSpendingGoal   `json:"message_spending_goal" bson:"message_spending_goal"`
	ShowOnAllPages           bool                  `json:"show_on_all_pages" bson:"show_on_all_pages"`
	PlacementSpendingGoal    PlacementSpendingGoal `json:"placement_spending_goal" bson:"placement_spending_goal"`
	CombineWithOtherDiscount bool                  `json:"combine_with_other_discount" bson:"combine_with_other_discount"`

	// orders limit
	OrderLimitSetting *OrderLimitSetting `json:"order_limit_setting,omitempty" bson:"order_limit_setting,omitempty"`

	// products limit
	ProductLimitSetting *ProductLimitSetting `json:"product_limit_setting,omitempty" bson:"product_limit_setting,omitempty"`

	// feature icons
	FeatureIcon *FeatureIcon `json:"feature_icon_setting,omitempty" bson:"feature_icon_setting,omitempty"`

	// comparision slider
	ComparisonSlider *ComparisonSlider `json:"comparison_slider_setting,omitempty" bson:"comparison_slider_setting,omitempty"`
}

type ComparisonSliderImage struct {
	ID    primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	URL   string             `json:"url" bson:"url"`
	Label string             `json:"label" bson:"label"`
	Text  string             `json:"text" bson:"text"`
}
type ComparisonSlider struct {
	Template     string                   `json:"template" bson:"template"`
	Images       *[]ComparisonSliderImage `json:"images,omitempty" bson:"images,omitempty"`
	Heading      string                   `json:"heading" bson:"heading"`
	Description  string                   `json:"description" bson:"description"`
	ButtonText   string                   `json:"button_text" bson:"button_text"`
	ButtonLink   string                   `json:"button_link" bson:"button_link"`
	ContentFirst bool                     `json:"content_first" bson:"content_first"`
}

func (db Database) CreateProductBlock(shop string, block ProductBlock) (*ProductBlock, error) {
	var b ProductBlock
	ctx := context.Background()
	filter := primitive.M{"shop": shop, "code": block.Code}
	opts := options.FindOneAndUpdate().SetUpsert(true)
	update := primitive.M{"$set": block}
	err := db.ProductBlock.FindOneAndUpdate(ctx, filter, update, opts).Decode(&b)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return &block, nil
		}
		return nil, err
	}
	return &b, nil
}

func (db Database) UpdateProductBlock(id string, block ProductBlockUpdate) (int64, error) {
	ctx := context.Background()
	objId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return 0, err
	}
	update := primitive.M{"$set": block}
	updateResult, err := db.ProductBlock.UpdateByID(ctx, objId, update)
	if err != nil {
		return 0, err
	}
	return updateResult.ModifiedCount, nil
}

func (db Database) UpdateProductBlockAppearance(shop, code string, appearance *Appearance) error {
	ctx := context.Background()
	filter := primitive.M{"shop": shop, "code": code}
	update := primitive.M{"$set": primitive.M{"appearance": appearance}}
	_, err := db.ProductBlock.UpdateOne(ctx, filter, update)
	return err
}

func (db Database) FindBlockByCode(shop, code string) (*ProductBlock, error) {
	ctx := context.Background()
	filter := primitive.M{"shop": shop, "code": code}
	var pb ProductBlock
	err := db.ProductBlock.FindOne(ctx, filter).Decode(&pb)
	if err != nil {
		return nil, err
	}
	return &pb, nil
}

func (db Database) FindAllProductBlock(shop string, code string) (*[]ProductBlock, error) {
	var blocks []ProductBlock
	ctx := context.Background()
	filter := primitive.M{"shop": shop}
	if code != "" {
		filter["code"] = code
	}
	cur, err := db.ProductBlock.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	if err = cur.All(ctx, &blocks); err != nil {
		return nil, err
	}
	return &blocks, nil
}

func (db Database) DeactivateProductBlock(shop string) error {
	ctx := context.Background()
	filter := primitive.M{"shop": shop}
	update := primitive.M{"$set": primitive.M{"is_active": false, "updated_at": time.Now(), "updated_by": "soulkeeper"}}
	_, err := db.ProductBlock.UpdateMany(ctx, filter, update)
	return err
}

var PaymentBadgeDefault = ProductBlock{
	Code:      "payment_badges",
	IsActive:  false,
	Template:  "",
	Heading:   "Multiple secure payment options available",
	Badges:    &[]string{"64c3ef669f5ff7f5930ecbc8", "64c3ef669f5ff7f5930ecc7e", "64c3ef669f5ff7f5930ecb13", "64c3ef669f5ff7f5930ecb0f", "64c3ef669f5ff7f5930ecc50"},
	CreatedBy: "system",
	CreatedAt: time.Now(),
	Appearance: &Appearance{
		Size: AppearanceSize{
			Desktop: 48,
			Mobile:  40,
		},
	},
}

var PaymentBadgeDefaultOnCart = ProductBlock{
	Code:      "payment_badges_cart",
	IsActive:  false,
	Template:  "",
	Heading:   "Multiple secure payment options available",
	Badges:    &[]string{"64c3ef669f5ff7f5930ecbc8", "64c3ef669f5ff7f5930ecc7e", "64c3ef669f5ff7f5930ecb13", "64c3ef669f5ff7f5930ecb0f", "64c3ef669f5ff7f5930ecc50"},
	CreatedBy: "system",
	CreatedAt: time.Now(),
	Position:  "above",
	Appearance: &Appearance{
		Size: AppearanceSize{
			Desktop: 48,
			Mobile:  40,
		},
	},
}

var TrustBadgeDefault = ProductBlock{
	Code:      "trust_badges",
	IsActive:  true,
	Template:  "",
	Heading:   "We keep your information and payment safe",
	Badges:    &[]string{"64d07270332267f496cec3ea", "64d07270332267f496cec3e6", "64d07270332267f496cec3ee", "64d07270332267f496cec3f0", "64d07270332267f496cec3f2", "64d07270332267f496cec3f5"},
	CreatedBy: "system",
	CreatedAt: time.Now(),
	Appearance: &Appearance{
		Size: AppearanceSize{
			Desktop: 48,
			Mobile:  40,
		},
	},
}

var TrustBadgeCartDefault = ProductBlock{
	Code:      "trust_badges_cart",
	IsActive:  false,
	Template:  "",
	Heading:   "We keep your information and payment safe",
	Badges:    &[]string{"64d07270332267f496cec3ea", "64d07270332267f496cec3e6", "64d07270332267f496cec3ee", "64d07270332267f496cec3f0", "64d07270332267f496cec3f2", "64d07270332267f496cec3f5"},
	CreatedBy: "system",
	Position:  "above",
	CreatedAt: time.Now(),
	Appearance: &Appearance{
		Size: AppearanceSize{
			Desktop: 48,
			Mobile:  40,
		},
	},
}

var ShippingInformationDefault = ProductBlock{
	Code:            "shipping_info",
	IsActive:        false,
	Template:        "default",
	Heading:         "Shipping information",
	DescriptionHtml: `<ul><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Free Shipping all orders over $50 USD</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Estimated delivery: 1 - 3 business days</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Tracking available: <a href="{shopDomain}" target="_blank" rel="noopener">link</a></span></li></ul>`,
	CreatedBy:       "system",
	CreatedAt:       time.Now(),
	Appearance: &Appearance{
		Template: "default",
		Color: AppearanceColor{
			Background: "#ffffff",
			Border:     "#d9d9d9",
			Text:       "#111111",
		},
	},
}

var RefundDefault = ProductBlock{
	Code:            "refund_info",
	IsActive:        false,
	Template:        "default",
	Heading:         "Refund information",
	DescriptionHtml: `<ul><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">30-day hassle-free returns</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">30-day money back guarantee</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">More details about Refund policy can be found <a href="{shopDomain}" target="_blank" rel="noopener">link</a></span></li></ul>`,
	CreatedBy:       "system",
	CreatedAt:       time.Now(),
	Appearance: &Appearance{
		Template: "default",
		Color: AppearanceColor{
			Background: "#ffffff",
			Border:     "#d9d9d9",
			Text:       "#111111",
		},
	},
}

var AdditionalInfoDefault = ProductBlock{
	Code:            "additional_info",
	IsActive:        false,
	Template:        "default",
	Heading:         "Special Instructions",
	DescriptionHtml: `<p style="color: rgb(109, 113, 117);">Here are some types of information you can display in this section:</p><ul><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">I SPENT 150$ AND SAVED 15$ FROM THE 10% DISCOUNT CODE, HOW GREAT!</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Already over 10,000 satisfied customers</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Quality of the product is consistently good. 5/5⭐</span></li><li style="color: rgb(109, 113, 117);"><span style="color: rgb(109, 113, 117);">Share this product on Instagram with hashtag #MYCHOICE to receive a discount code 15% off</span></li></ul>`,
	CreatedBy:       "system",
	CreatedAt:       time.Now(),
	Appearance: &Appearance{
		Template: "default",
		Color: AppearanceColor{
			Background: "#ffffff",
			Border:     "#d9d9d9",
			Text:       "#111111",
		},
	},
}

var CountdownTimerCartDefault = ProductBlock{
	Code:             "countdown_timer_cart",
	IsActive:         false,
	OnItEndAction:    "hide",
	AnnouncementText: "Your products are reserved for {timer}",
	Template:         "default",
	Position:         "top",
	CreatedBy:        "system",
	CreatedAt:        time.Now(),
	Timer:            5,
	Appearance: &Appearance{
		Template: "default",
		Color: AppearanceColor{
			Background: "#D4E3F0",
			Text:       "#111111",
		},
		Position: "top",
	},
}

var CountdownTimerProductDefault = ProductBlock{
	Code:             "countdown_timer_product",
	IsActive:         false,
	OnItEndAction:    "hide",
	AnnouncementText: "Your cart are reserved for {timer}",
	Template:         "default",
	CreatedBy:        "system",
	CreatedAt:        time.Now(),
	Timer:            5,
	Appearance: &Appearance{
		Template: "default",
		Color: AppearanceColor{
			Background: "#D4E3F0",
			Text:       "#111111",
		},
	},
}

var StockCountdownDefault = ProductBlock{
	Code:              "stock_countdown",
	IsActive:          false,
	Template:          "default",
	AnnouncementText:  "Only {stock_quantity} left in stock. Hurry up!",
	WhenItShowAction:  "always",
	QuantityCondition: 10,
	Appearance: &Appearance{
		Template: "default",
		Color: AppearanceColor{
			Background: "#D4E3F0",
			Text:       "#111111",
		},
	},
}

var FreeShippingBarDefault = ProductBlock{
	Code:           "free_shipping_bar",
	OrderValue:     100,
	TextBefore:     "Free shipping for orders over {order_value}",
	TextInProgress: "Only {order_value_progress} away from free shipping",
	TextGoal:       "Congratulation! You have got Free shipping",
	IsActive:       false,
	Appearance: &Appearance{
		Color: AppearanceColor{
			Background: "#043BA6",
			Text:       "#ffffff",
		},
	},
}

var SalePopupDefault = ProductBlock{
	Code:           "sales_pop_up",
	IsActive:       false,
	OrderStatus:    &[]string{"open", "archived"},
	SalesPopupText: "{customer_full_name} from {city}, {country_code} bought\n{product_name}\n{time_ago}",
	Placement:      "allPages",
	Timing: &struct {
		First    int64 "json:\"first\" bson:\"first\""
		Duration int64 "json:\"duration\" bson:\"duration\""
		Delay    int64 "json:\"delay\" bson:\"delay\""
	}{
		First:    0,
		Duration: 10,
		Delay:    5,
	},
	PositionSalePopup: &struct {
		ShowDesktop bool   "json:\"show_desktop\" bson:\"show_desktop\""
		ShowMobile  bool   "json:\"show_mobile\" bson:\"show_mobile\""
		Desktop     string "json:\"desktop\" bson:\"desktop\""
		Mobile      string "json:\"mobile\" bson:\"mobile\""
	}{
		ShowDesktop: true,
		ShowMobile:  true,
		Desktop:     "bottomLeft",
		Mobile:      "top",
	},
	SpecificPages:   &[]string{"index", "product"},
	BackgroundColor: "#333333FF",
	TextColor:       "#FFFFFFFF",
	OrderCreatedAt:  -7,
	Trademark:       false,
}

var CookieBannerDefault = ProductBlock{
	Code:             "cookie_banner",
	ConfirmationText: "🍪 This website uses cookies to ensure you get the best experience on our website.",
	ButtonText:       "Accept",
	Privacy:          true,
	PrivacyLabel:     "Learn more",
	PrivacyLink:      "",
	CloseButton:      true,
	ShowBanner:       "all",
	IsActive:         false,
	Appearance: &Appearance{
		Color: AppearanceColor{
			Background:       "#111111",
			Text:             "#FFFFFF",
			ButtonBackground: "#ffffff",
			ButtonTextColor:  "#111111",
		},
	},
}

var TermConditionDefault = ProductBlock{
	Code:              "agree_to_terms_checkbox",
	IsActive:          false,
	TermConditionText: "I have read and agreed to the {store_name}",
	AlertText:         "Please agree to the terms and conditions before making a purchase!",
	Privacy:           true,
	PrivacyLabel:      "Terms and Conditions",
	PrivacyLink:       "",
	Appearance: &Appearance{
		Color: AppearanceColor{
			Checkbox: "#333333",
			Warning:  "#8E1F0B",
		},
	},
}

var StickyAddToCartDefault = ProductBlock{
	Code:       "sticky_add_to_cart",
	IsActive:   false,
	ButtonText: "Add to cart",
	Appearance: &Appearance{
		Color: AppearanceColor{
			Background:  "#F8F8F8",
			Text:        "#111111",
			ButtonColor: "#111111",
		},
	},
}

var AddToCartAnimator = ProductBlock{
	Code:      "add_to_cart_animation",
	IsActive:  false,
	Animation: "shake",
}

var SizeChartDefault = ProductBlock{
	Code:              "size_chart",
	IsActive:          false,
	SizeChartPosition: []string{"inline"},
	SizeChartText:     "Size chart",
	SizeChartList:     nil,
	Appearance: &Appearance{
		Color: AppearanceColor{
			ButtonBackground: "#ffffff",
			ButtonTextColor:  "#111111",
			ButtonLinkColor:  "#111111",
		},
		Position: "inline",
	},
}

var FaviconCartCountDefault = ProductBlock{
	Code:            "favicon_cart_count",
	IsActive:        false,
	BackgroundColor: "#C80B0BFF",
	TextColor:       "#FFFFFFFF",
}

var InactiveTabDefault = ProductBlock{
	Code:     "inactive_tab",
	IsActive: false,
	Heading:  "🙂 Don't forget your items! Complete your purchase now 🎉",
}

var ScrollToTopButtonDefault = ProductBlock{
	Code:            "scroll_to_top_button",
	IsActive:        false,
	Badge:           "circle",
	Icon:            "triple-chevron",
	Style:           "outline",
	BackgroundColor: "#1B1B1B",
}

var AutoExternalLinkDefault = ProductBlock{
	Code:     "auto_external_links",
	IsActive: false,
}

var SocialMediaButtonsDefault = ProductBlock{
	Code:     "social_media_buttons",
	IsActive: false,
	Template: "circle",
	PositionMediaButtons: struct {
		ShowDesktop bool   "json:\"show_desktop\" bson:\"show_desktop\""
		ShowMobile  bool   "json:\"show_mobile\" bson:\"show_mobile\""
		Desktop     string "json:\"desktop\" bson:\"desktop\""
		Mobile      string "json:\"mobile\" bson:\"mobile\""
	}{
		ShowDesktop: true,
		ShowMobile:  true,
		Desktop:     "bottomRight",
		Mobile:      "bottomRight",
	},
	Links: []struct {
		Key  string `json:"key" bson:"key"`
		Link string `json:"link" bson:"link"`
	}{
		{
			Key:  "facebook",
			Link: "",
		},
		{
			Key:  "instagram",
			Link: "",
		},
		{
			Key:  "tiktok",
			Link: "",
		},
		{
			Key:  "youtube",
			Link: "",
		},
		{
			Key:  "x",
			Link: "",
		},
		{
			Key:  "linkedin",
			Link: "",
		},
		{
			Key:  "discord",
			Link: "",
		},
		{
			Key:  "snapchat",
			Link: "",
		},
		{
			Key:  "pinterest",
			Link: "",
		},
		{
			Key:  "tumblr",
			Link: "",
		},
	},
}

var ContentProtectionDefault = ProductBlock{
	Code:     "content_protection",
	IsActive: false,
	Disable:  []string{"commonShortcut", "rightClick", "textSelection", "dragDrop"},
	ApplyFor: "all",
}

var BestSellerProtectionDefault = ProductBlock{
	Code:     "best_sellers_protection",
	IsActive: false,
}

var ProductLabelsAndBadgesDefault = ProductBlock{
	Code:                   "product_labels",
	IsActive:               false,
	ProductLabelsAndBadges: &[]ProductLabelAndBadge{},
}

var ProductTabsAndAccordionDefault = ProductBlock{
	Code:                           "product_tabs_and_accordion",
	IsActive:                       false,
	ProductTabHeadingSelector:      "h5",
	ProductTabDisplay:              "horizontal",
	ProductTabHorizontalAutoSwitch: true,
	ProductTabAccordionStyle:       "all_closed",
	ProductTabTitle:                "“Description”",
}

var MessageScrollingTextBannerDefault = []MessageScrollingTextBanner{
	{
		ID:      primitive.NewObjectID(),
		Message: "FREE SHIPPING FROM $100",
		Link:    "",
		Icon:    "DeliveryIcon",
	},
	{
		ID:      primitive.NewObjectID(),
		Message: "SECURE PAYMENT & CHECKOUT",
		Link:    "",
		Icon:    "CreditCardIcon",
	},
	{
		ID:      primitive.NewObjectID(),
		Message: "ECO-FRIENDLY",
		Link:    "",
		Icon:    "NatureIcon",
	},
}

var ScrollingTextBannerDefault = ProductBlock{
	Code:     "scrolling_text_banner",
	IsActive: false,
	Messages: MessageScrollingTextBannerDefault,
	Appearance: &Appearance{
		Color: AppearanceColor{
			Background: "#F6F6F6FF",
			Text:       "#111111E6",
		},
		Size: AppearanceSize{
			Mobile: 14,
		},
	},
	ScrollingTextBanner: true,
	ScrollingSpeed:      10,
	PauseOnMouseover:    false,
}

var SpendingGoalTrackerDefault = ProductBlock{
	Code:     "spending_goal_tracker",
	IsActive: false,
	Goal:     100,
	Discount: SpendingGoalDiscount{
		Value: 10,
		Type:  "percentage",
	},
	CombineWithOtherDiscount: true,
	MessageSpendingGoal: MessageSpendingGoal{
		Initial:  "Spend {spending_goal} to get {discount_value} off!",
		Progress: "Spend just {remaining_goal} more to receive {discount_value} discount.",
		Reached:  "Congratulations! You 've earned {discount_value} off on this order.",
	},
	PlacementSpendingGoal: PlacementSpendingGoal{
		ShowOnAllPages: true,
		Placement: map[string]string{
			"index":            "Homepage",
			"product":          "Product pages",
			"list-collections": "Collections list page",
			"collection":       "Collections page",
			"cart":             "Cart page",
		},
	},
	Appearance: &Appearance{
		Template: "circle",
		Color: AppearanceColor{
			Background:     "#FFFFFFFF",
			Text:           "#111111FF",
			HighlightColor: "#FE5303FF",
		},
		ShowOn:         []string{"desktop", "mobile"},
		Position:       "bottom_left",
		PositionMobile: "top",
	},
}

var OrderLimitSettingDefault = ProductBlock{
	Code:     "order_limit",
	IsActive: false,
	OrderLimitSetting: &OrderLimitSetting{
		ProductQuantity: &ItemLimitSetting{
			Active: true,
			Setting: LimitSetting{
				MinValue:        1,
				MaxValue:        100,
				Unit:            "pcs",
				MinMessageReach: "You must select at least {minimum_order_quantity} products.",
				MaxMessageReach: "You can select a maximum of {maximum_order_quantity} products.",
			},
		},
	},
	Appearance: &Appearance{
		Color: AppearanceColor{
			Background: "#F8CC2CFF",
			Text:       "#5A4600FF",
		},
	},
}

var ProductLimitSettingDefault = ProductBlock{
	Code:     "product_limit",
	IsActive: false,
	ProductLimitSetting: &ProductLimitSetting{
		Products: &[]ProductItemSetting{},
		Message: &MessageProductLimit{
			MinMessageReach: "You must select at least {minimum_product_quantity} products",
			MaxMessageReach: "You can only select a maximum of {maximum_product_quantity} products",
		},
	},
	Appearance: &Appearance{
		Color: AppearanceColor{
			Background: "#FFE27BFF",
			Text:       "#5A4600FF",
		},
	},
}

var FeatureIconDefault = ProductBlock{
	Code:     "feature_icon",
	IsActive: false,
	FeatureIcon: &FeatureIcon{
		Heading: "WHY CHOOSING US?",
		Items: &[]FeatureIconItem{
			{
				ID:          primitive.NewObjectID(),
				Icon:        "https://cdn.trustz.app/assets/images/polaris-icons/DeliveryIcon.svg",
				Title:       "FREE DELIVERY",
				Description: "Order today, receive tomorrow with fast, free shipping",
			},
		},
	},
	Appearance: &Appearance{
		Color: AppearanceColor{
			Background: "#F0F0F0FF",
			Text:       "#111111E6",
			IconColor:  "#111111E6",
		},
	},
}

var ComparisonSliderDefault = ProductBlock{
	Code:     "comparison_slider",
	IsActive: false,
	ComparisonSlider: &ComparisonSlider{
		Template: "default",
		Images: &[]ComparisonSliderImage{
			{
				ID:    primitive.NewObjectID(),
				URL:   "",
				Label: "Before",
				Text:  "Before",
			},
			{
				ID:    primitive.NewObjectID(),
				URL:   "",
				Label: "After",
				Text:  "After",
			},
		},
		Heading:      "",
		Description:  "",
		ButtonText:   "",
		ButtonLink:   "",
		ContentFirst: true,
	},
	Appearance: &Appearance{
		Template: "default",
		Color: AppearanceColor{
			Background:       "#FFFFFFFF",
			Text:             "#212121FF",
			ButtonBackground: "#212121FF",
			ButtonTextColor:  "#FFFFFFFF",
		},
	},
}

var DefaultProductBlockSetting = map[string]*ProductBlock{
	"payment_badges":             &PaymentBadgeDefault,
	"trust_badges":               &TrustBadgeDefault,
	"shipping_info":              &ShippingInformationDefault,
	"refund_info":                &RefundDefault,
	"additional_info":            &AdditionalInfoDefault, // special instructions
	"countdown_timer_cart":       &CountdownTimerCartDefault,
	"countdown_timer_product":    &CountdownTimerProductDefault,
	"stock_countdown":            &StockCountdownDefault,
	"free_shipping_bar":          &FreeShippingBarDefault,
	"sales_pop_up":               &SalePopupDefault,
	"payment_badges_cart":        &PaymentBadgeDefaultOnCart,
	"trust_badges_cart":          &TrustBadgeCartDefault,
	"cookie_banner":              &CookieBannerDefault,
	"sticky_add_to_cart":         &StickyAddToCartDefault,
	"add_to_cart_animation":      &AddToCartAnimator,
	"size_chart":                 &SizeChartDefault,
	"favicon_cart_count":         &FaviconCartCountDefault,
	"inactive_tab":               &InactiveTabDefault,
	"scroll_to_top_button":       &ScrollToTopButtonDefault,
	"auto_external_link":         &AutoExternalLinkDefault,
	"social_media_buttons":       &SocialMediaButtonsDefault,
	"content_protection":         &ContentProtectionDefault,
	"best_sellers_protection":    &BestSellerProtectionDefault,
	"product_labels":             &ProductLabelsAndBadgesDefault,
	"product_tabs_and_accordion": &ProductTabsAndAccordionDefault,
	"agree_to_terms_checkbox":    &TermConditionDefault,
	"scrolling_text_banner":      &ScrollingTextBannerDefault,
	"spending_goal_tracker":      &SpendingGoalTrackerDefault,
	"order_limit":                &OrderLimitSettingDefault,
	"product_limit":              &ProductLimitSettingDefault,
	"feature_icon":               &FeatureIconDefault,
	"comparison_slider":          &ComparisonSliderDefault,
}

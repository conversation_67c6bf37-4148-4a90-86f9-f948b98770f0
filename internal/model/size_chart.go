package model

import (
	"context"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Measurement struct {
	Key   string `json:"key" bson:"key"`
	Value string `json:"value" bson:"value"`
}

type Size struct {
	Legend       string        `json:"legend" bson:"legend"`
	Size         string        `json:"size" bson:"size"`
	Measurements []Measurement `json:"measurements" bson:"measurements"`
}

type SizeChartCategory struct {
	Name        string `json:"name" bson:"name"`
	Icon        string `json:"icon" bson:"icon"`
	Category    string `json:"category" bson:"category"`
	Position    int32  `json:"position" bson:"position"`
	Description string `json:"description" bson:"description"`
	Image       string `json:"image" bson:"image"`
	Sises       []Size `json:"sizes" bson:"sizes"`
}

func (db *Database) GetSizeChartCategories(ctx context.Context) (*[]SizeChartCategory, error) {
	var cats []SizeChartCategory
	opts := options.Find()
	opts.SetSort(primitive.M{"position": 1})
	cur, err := db.SizeChartCategory.Find(ctx, primitive.M{}, opts)
	if err != nil {
		return nil, err
	}
	err = cur.All(ctx, &cats)
	if err != nil {
		return nil, err
	}
	return &cats, nil
}

package model

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Soulkeeper struct {
	ID           primitive.ObjectID `bson:"_id"`
	Enable       bool               `bson:"enable"`
	ToastMessage string             `bson:"toast_message" json:"toast_message"`
	AppEncoded   string             `bson:"app_encoded" json:"app_encoded"`
	CookieExpire int64              `bson:"cookie_expire" json:"cookie_expire"`
}

func (d *Database) FindSoulkeeper() (Soulkeeper, error) {
	var soulkeeper Soulkeeper
	err := d.Soulkeeper.FindOne(context.Background(), bson.M{}).Decode(&soulkeeper)
	return soulkeeper, err
}

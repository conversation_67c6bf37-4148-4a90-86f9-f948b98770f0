package model

import (
	"context"
	"os"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Database struct {
	Badge             *mongo.Collection
	ProductBlock      *mongo.Collection
	GenerativeAi      *mongo.Collection
	Blacklist         *mongo.Collection
	Theme             *mongo.Collection
	SizeChartCategory *mongo.Collection
	Soulkeeper        *mongo.Collection
	ProductLabel      *mongo.Collection
	ExtensionConfig   *mongo.Collection
	File              *mongo.Collection
	Banner            *mongo.Collection
	InsuranceAddon    *mongo.Collection
	Shop              *mongo.Collection
}

func NewMongoDB(dsn string) *Database {
	ctx := context.Background()
	serverAPIOptions := options.ServerAPI(options.ServerAPIVersion1)
	clientOptions := options.Client().ApplyURI(dsn).SetServerAPIOptions(serverAPIOptions)
	client, _ := mongo.Connect(ctx, clientOptions)
	err := client.Ping(ctx, nil)
	if err != nil {
		panic(err)
	}
	db := client.Database(os.Getenv("MONGO_DBNAME"))
	return &Database{
		Badge:             db.Collection("badges"),
		ProductBlock:      db.Collection("product_blocks"),
		GenerativeAi:      db.Collection("generative_ai"),
		Blacklist:         db.Collection("black_list"),
		Theme:             db.Collection("themes"),
		SizeChartCategory: db.Collection("size_chart_categories"),
		Soulkeeper:        db.Collection("soulkeeper"),
		ProductLabel:      db.Collection("product_labels"),
		ExtensionConfig:   db.Collection("extension_config"),
		File:              db.Collection("files"),
		Banner:            db.Collection("banners"),
		InsuranceAddon:    db.Collection("insurance_addon"),
		Shop:              db.Collection("shops"),
	}
}

package model

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ThemeCountdownSelector struct {
	Selectors ProductBlockSelector `json:"selectors" bson:"selectors"`
	StyleCss  string               `json:"style_css" bson:"style_css"`
}

type ThemeQuoteSelector struct {
	Selectors      []string    `json:"selectors,omitempty" bson:"selectors,omitempty"`
	IframeTypes    interface{} `json:"iframeTypes,omitempty" bson:"iframeTypes,omitempty"`
	ShadowDomTypes interface{} `json:"shadowDomTypes,omitempty" bson:"shadowDomTypes,omitempty"`
	Mutation       []string    `json:"mutations,omitempty" bson:"mutations,omitempty"`
	Trigger        interface{} `json:"trigger,omitempty" bson:"trigger,omitempty"`
	StyleCss       string      `json:"styleCss" bson:"styleCss"`
	FullWidth      bool        `json:"full_width" bson:"full_width"`
}

type Theme struct {
	ID           primitive.ObjectID      `json:"_id,omitempty" bson:"_id,omitempty"`
	Name         string                  `json:"name,omitempty" bson:"name,omitempty"`
	Countdown    *ThemeCountdownSelector `json:"countdown" bson:"countdown"`
	CreatedAt    time.Time               `json:"created_at" bson:"created_at,omitempty"`
	UpdatedAt    time.Time               `json:"updated_at" bson:"updated_at,omitempty"`
	CreatedBy    string                  `json:"created_by" bson:"created_by"`
	UpdatedBy    string                  `json:"updated_by" bson:"updated_by"`
	ThemeStoreId interface{}             `json:"theme_store_id" bson:"theme_store_id"`
	Quote        *ThemeQuoteSelector     `json:"quote" bson:"quote"`
}

func (db Database) CreateTheme(theme Theme) (*Theme, error) {
	var t Theme
	ctx := context.Background()
	filter := primitive.M{"name": theme.Name}
	opts := options.FindOneAndUpdate().SetUpsert(true)
	update := primitive.M{"$set": theme}
	err := db.Theme.FindOneAndUpdate(ctx, filter, update, opts).Decode(&t)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return &t, nil
		}
		return nil, err
	}
	return &t, nil
}

func (db Database) FindTheme(name string) (*Theme, error) {
	var t Theme
	ctx := context.Background()
	filter := primitive.M{"name": name}
	err := db.Theme.FindOne(ctx, filter).Decode(&t)
	if err != nil {
		return nil, err
	}
	return &t, nil
}

package api

import (
	"context"
	"encoding/base64"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/jangnh/amote/internal/model"
	"github.com/jangnh/amote/internal/repositories"
	"github.com/jangnh/amote/pkg/log"
	"github.com/jangnh/amote/pkg/mail"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/golang-jwt/jwt/v5"
	"github.com/jangnh/amote/app"
	"github.com/labstack/echo/v4"
)

var Db = model.NewMongoDB(os.Getenv("MONGO_URI"))

type Routes struct {
	Auth           *echo.Group
	Quote          *echo.Group
	Webhook        *echo.Group
	Gdpr           *echo.Group
	Shop           *echo.Group
	Theme          *echo.Group
	ProductBlock   *echo.Group
	Badge          *echo.Group
	Generative     *echo.Group
	BlackList      *echo.Group
	Loyalty        *echo.Group
	Metadata       *echo.Group
	Order          *echo.Group
	File           *echo.Group
	Statistic      *echo.Group
	Category       *echo.Group
	InsuranceAddon *echo.Group
}

type Repos struct {
	Store         *repositories.StoreRepo
	Quote         *repositories.QuoteRepo
	QuoteCategory *repositories.QuoteCategoryRepo
}

type Api struct {
	Server          *app.Server
	AdminRoute      *Routes
	StorefrontRoute *Routes
	WebhookRoute    *Routes
	BaseRoute       *Routes
	Repo            *Repos
}

type ApiResponse struct {
	Status bool        `json:"status"`
	Data   interface{} `json:"data"`
	Error  string      `json:"error"`
}

func (api *Api) ValidateAuthorization(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		tokenString := c.Request().Header.Get("Authorization")
		tokenString = strings.TrimSpace(strings.TrimPrefix(tokenString, "Bearer"))
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return []byte(api.Server.Config.ShopifyApiSecret), nil
		})
		if err != nil {
			return c.String(http.StatusBadRequest, err.Error())
		}

		claims := token.Claims

		iss, err := claims.GetIssuer()
		if err != nil {
			return c.String(http.StatusBadRequest, err.Error())
		}
		if !strings.Contains(iss, ".myshopify.com") {
			return c.String(http.StatusBadRequest, "token not provided by shopify.com")
		}
		aud, err := claims.GetAudience()
		if err != nil {
			return c.String(http.StatusBadRequest, err.Error())
		}
		// cheat
		if aud[0] != api.Server.Config.ShopifyApiKey {
			return c.String(http.StatusBadRequest, "audience invalid")
		}
		shop := strings.TrimPrefix(strings.ReplaceAll(iss, "/admin", ""), "https://")
		store, err := api.Repo.Store.FindOne(shop)
		if err != nil || !store.IsActive {
			return c.String(http.StatusForbidden, err.Error())
		}
		if store.IsBanned {
			return c.NoContent(http.StatusForbidden)
		}
		c.Set("shop", store.Shop)
		c.Set("host", store.Host)
		c.Set("access_token", store.AccessToken)
		c.Set("store", store)
		return next(c)
	}
}

func (api *Api) AddDefaultData(c echo.Context) error {
	filter := primitive.M{}
	codes := c.QueryParam("codes")
	ctx := context.TODO()
	cursor, err := api.Repo.Store.Collection.Find(ctx, filter)
	if err != nil {
		slog.Error("find stores error", "error", err.Error())
	}
	var stores []model.Store
	err = cursor.All(ctx, &stores)
	if err != nil {
		slog.Error("decode stores error", "error", err.Error())
	}
	for _, code := range strings.Split(codes, ",") {
		block := model.DefaultProductBlockSetting[code]
		// loop all stores
		for _, store := range stores {
			go func(s model.Store, block *model.ProductBlock) {
				slog.Info("processing for shop", "shop", s.Shop)
				filter := primitive.M{"code": block.Code, "shop": s.Shop}
				result := Db.ProductBlock.FindOne(ctx, filter)
				if result.Err() == mongo.ErrNoDocuments {
					slog.Info("document not found", "shop", s.Shop, "code", block.Code)
					block.Shop = s.Shop
					block.CreatedBy = "system"
					block.UpdateAt = time.Now()
					Db.CreateProductBlock(s.Shop, *block)
				}
			}(store, block)
		}
	}

	return nil
}

func (api *Api) AddQuoteDefaultData(c echo.Context) error {
	filter := primitive.M{}
	ctx := context.TODO()
	cursor, err := api.Repo.Store.Collection.Find(ctx, filter)
	if err != nil {
		slog.Error("find stores error", "error", err.Error())
	}
	var stores []model.Store
	err = cursor.All(ctx, &stores)
	if err != nil {
		slog.Error("decode stores error", "error", err.Error())
	}
	for _, store := range stores {
		go func(s model.Store) {
			slog.Info("processing for shop", "shop", s.Shop)
			quotes, err := api.Repo.Quote.FindQuote(s.Shop)
			if err != nil {
				slog.Error("find quotes error", "error", err.Error())
			}
			if len(*quotes) == 0 {
				slog.Info("document not found", "shop", s.Shop)
				quote := model.DefaultQuoteCart
				quote.Shop = s.Shop
				api.Repo.Quote.CreateNewQuote(s.Shop, quote)
			}
		}(store)
	}
	return nil
}

func (api *Api) SendEmailLoyalty(c echo.Context) error {
	shop := c.QueryParam("shop")
	store, _ := api.Repo.Store.FindOne(shop)
	decodedHost, _ := base64.RawStdEncoding.DecodeString(store.Host)
	data := map[string]interface{}{
		"store_name": store.StoreName,
		"store_url":  string(decodedHost),
	}
	now := time.Now()
	sendAt := int(now.Unix()) // schedule email send after 24h
	result, err := mail.Send(store.StoreName, store.Email, sendAt, data)
	if err != nil {
		log.Logger.Error("send email loyalty quest error", "shop", store.Shop, "to", store.Email, "error", err.Error())
	}
	if err == nil {
		log.Logger.Info("send email loyalty quest successfully", "store_name", store.StoreName, "shop", store.Shop, "to", store.Email, "result", result)
	}
	return c.NoContent(http.StatusOK)
}

func (api *Api) UpdateProductBlockAppearance(c echo.Context) error {
	// tim tất cả shop không phải trial
	filter := primitive.M{"shopify_plan_name": primitive.M{"$nin": []string{"trial"}}}
	ctx := context.TODO()
	cursor, err := api.Repo.Store.Collection.Find(ctx, filter)
	if err != nil {
		slog.Error("find stores error", "error", err.Error())
	}
	var stores []model.Store
	err = cursor.All(ctx, &stores)
	if err != nil {
		slog.Error("decode stores error", "error", err.Error())
	}
	blocksUpdate := []string{
		"countdown_timer_cart",
		"countdown_timer_product",
		"stock_countdown",
		"free_shipping_bar",
		"sticky_add_to_cart",
		"shipping_info",
		"refund_info",
		"additional_info",
		"size_chart",
		"agree_to_terms_checkbox",
		"cookie_banner",
		"payment_badges",
		"trust_badges",
		"trust_badges_cart",
		"payment_badges_cart",
	}
	// loop all stores
	for _, store := range stores {
		go func(s model.Store) {
			for _, block := range blocksUpdate {
				slog.Info("processing for shop", "shop", s.Shop)
				filter := primitive.M{"code": block, "shop": s.Shop}
				result := Db.ProductBlock.FindOne(ctx, filter)
				if result.Err() == mongo.ErrNoDocuments {
					slog.Info("create new product block", "shop", s.Shop, "code", block)
					bl := model.DefaultProductBlockSetting[block]
					bl.Shop = s.Shop
					bl.CreatedBy = "system"
					bl.UpdateAt = time.Now()
					Db.CreateProductBlock(s.Shop, *bl)
				} else {
					slog.Info("update appearance product block", "shop", s.Shop, "code", block)
					var bl model.ProductBlock
					err := result.Decode(&bl)
					if err != nil {
						slog.Error("decode product block error", "shop", s.Shop, "code", block, "error", err.Error())
					}
					appearance := model.DefaultProductBlockSetting[block].Appearance
					Db.UpdateProductBlockAppearance(s.Shop, block, appearance)
				}
			}
		}(store)
	}

	return nil
}

func Init(s *app.Server) *Api {
	storeRepo := repositories.NewStoreRepo(s.DB)
	quoteRepo := repositories.NewQuoteRepo(s.DB)
	quoteCategoryRepo := repositories.NewQuoteCategoryRepo(s.DB)
	api := &Api{
		Server:          s,
		BaseRoute:       &Routes{},
		AdminRoute:      &Routes{},
		WebhookRoute:    &Routes{},
		StorefrontRoute: &Routes{},
		Repo:            &Repos{},
	}

	// define repos
	api.Repo.Store = &storeRepo
	api.Repo.Quote = &quoteRepo
	api.Repo.QuoteCategory = &quoteCategoryRepo

	// define auth sub router
	api.BaseRoute.Auth = api.Server.Echo.Group("/auth")
	api.BaseRoute.Category = api.Server.Echo.Group("/quote-categories")

	// define admin sub router
	adminGroup := api.Server.Echo.Group("/admin")
	adminGroup.Use(api.ValidateAuthorization)
	api.AdminRoute.Shop = adminGroup.Group("/shop")
	api.AdminRoute.Loyalty = adminGroup.Group("/loyalty")
	api.AdminRoute.Theme = adminGroup.Group("/themes")
	api.AdminRoute.Badge = adminGroup.Group("/badges")
	api.AdminRoute.ProductBlock = adminGroup.Group("/product_blocks")
	api.AdminRoute.Generative = adminGroup.Group("/generative")
	api.AdminRoute.BlackList = adminGroup.Group("/blacklist")
	api.AdminRoute.Metadata = adminGroup.Group("/metadata")
	api.AdminRoute.Loyalty = adminGroup.Group("/loyalty")
	api.AdminRoute.File = adminGroup.Group("/files")
	api.AdminRoute.Statistic = adminGroup.Group("/statistic")
	api.AdminRoute.Quote = adminGroup.Group("/quotes")
	api.AdminRoute.Category = adminGroup.Group("/categories")
	api.AdminRoute.InsuranceAddon = adminGroup.Group("/insurance_addons")
	// define webhooks sub router
	webhookGroup := api.Server.Echo.Group("/webhooks")
	api.WebhookRoute.Webhook = webhookGroup.Group("/events")
	api.WebhookRoute.Gdpr = webhookGroup.Group("/gdpr")

	// define storefront sub router
	storefrontGroup := api.Server.Echo.Group("/storefront")
	api.StorefrontRoute.ProductBlock = storefrontGroup.Group("/product_blocks")
	api.StorefrontRoute.Order = storefrontGroup.Group("/orders")
	api.StorefrontRoute.Quote = storefrontGroup.Group("/quotes")
	api.StorefrontRoute.InsuranceAddon = storefrontGroup.Group("/insurance_addons")
	api.Server.Echo.GET("/internal/add-default-data", api.AddDefaultData)
	api.Server.Echo.GET("/internal/add-quote-default-data", api.AddQuoteDefaultData)
	api.Server.Echo.GET("/internal/update-appearance", api.UpdateProductBlockAppearance)
	api.Server.Echo.GET("/internal/send-email-loyalty", api.SendEmailLoyalty)

	api.Server.Echo.GET("/sk", api.GetSoulkeeper, api.SoulkeeperAuthorization)
	api.Server.Echo.PUT("/sk/dw", api.UpdateSoulkeeper, api.SoulkeeperAuthorization)
	// init routes
	api.initAuth()
	api.initWebhook()
	api.initQuote()
	api.initGdpr()
	api.initShopRoute()
	api.initThemeRoute()
	api.initProductBlockRoute()
	api.initBadgeRoute()
	api.initGenerativeRoute()
	api.initBlackListRoute()
	api.initLoyaltyRoute()
	api.initMetadataRoute()
	api.initOrderRoute()
	api.initFileRoute()
	api.initStatisticRoute()
	api.initInsuranceAddonRoute()

	return api
}

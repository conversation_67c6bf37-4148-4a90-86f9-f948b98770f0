package api

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	goshopify "github.com/bold-commerce/go-shopify"
	"github.com/jangnh/amote/internal/model"
	"github.com/jangnh/amote/internal/server"
	"github.com/jangnh/amote/internal/shopify"
	"github.com/jangnh/amote/pkg/log"
	"github.com/labstack/echo/v4"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (api *Api) ListProductBlock(c echo.Context) error {
	shop := c.Get("shop").(string)
	blocks, err := Db.FindAllProductBlock(shop, "")
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	// add default setting
	var _blks []model.ProductBlock
	for _, block := range *blocks {
		block.Default = model.DefaultProductBlockSetting[block.Code]
		_blks = append(_blks, block)
	}
	return c.JSON(http.StatusOK, _blks)
}

func (api *Api) ListProductBlockStorefront(c echo.Context) error {
	shop := c.QueryParam("shop")
	code := c.QueryParam("code")
	blocks, err := Db.FindAllProductBlock(shop, code)
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	store, _ := api.Repo.Store.FindOne(shop)
	if store.IsBanned {
		return c.NoContent(http.StatusForbidden)
	}
	// find selector in theme
	theme, err := Db.FindTheme(store.ThemeName)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// get theme default selector
			theme, err = Db.FindTheme("DefaultSelector")
		}
	}
	var prodBlocks []model.ProductBlock
	for _, block := range *blocks {
		badges := block.Badges
		var badgeSrc []string
		if badges != nil {
			for _, id := range *badges {
				badge, err := Db.FindBadgeById(id)
				if err == nil {
					badgeSrc = append(badgeSrc, badge.Url)
				}
			}
		}

		if err == nil {
			if theme.Countdown != nil {
				// log.Logger.Info("update reward selectors with theme selectors", "shop", store.Shop, "theme_name", theme.Name)
				if theme.Countdown.StyleCss != "" {
					block.StyleCss = theme.Countdown.StyleCss
				}
				block.Selectors = &theme.Countdown.Selectors
			}
		}

		prodBlocks = append(prodBlocks, model.ProductBlock{
			ID:                             block.ID,
			Badges:                         &badgeSrc,
			DescriptionHtml:                block.DescriptionHtml,
			Heading:                        block.Heading,
			IsActive:                       block.IsActive,
			Code:                           block.Code,
			StyleCss:                       block.StyleCss,
			Template:                       block.Template,
			Shop:                           block.Shop,
			AnnouncementText:               block.AnnouncementText,
			OnItEndAction:                  block.OnItEndAction,
			Timer:                          block.Timer,
			Position:                       block.Position,
			Selectors:                      block.Selectors,
			QuantityCondition:              block.QuantityCondition,
			WhenItShowAction:               block.WhenItShowAction,
			TextBefore:                     block.TextBefore,
			TextInProgress:                 block.TextInProgress,
			OrderValue:                     block.OrderValue,
			TextGoal:                       block.TextGoal,
			SalesPopupText:                 block.SalesPopupText,
			Placement:                      block.Placement,
			Timing:                         block.Timing,
			PositionSalePopup:              block.PositionSalePopup,
			SpecificPages:                  block.SpecificPages,
			ConfirmationText:               block.ConfirmationText,
			ButtonText:                     block.ButtonText,
			Privacy:                        block.Privacy,
			PrivacyLabel:                   block.PrivacyLabel,
			PrivacyLink:                    block.PrivacyLink,
			CloseButton:                    block.CloseButton,
			ShowBanner:                     block.ShowBanner,
			TermConditionText:              block.TermConditionText,
			AlertText:                      block.AlertText,
			BackgroundColor:                block.BackgroundColor,
			TextColor:                      block.TextColor,
			Animation:                      block.Animation,
			SizeChartText:                  block.SizeChartText,
			SizeChartPosition:              block.SizeChartPosition,
			SizeChartList:                  block.SizeChartList,
			Badge:                          block.Badge,
			Icon:                           block.Icon,
			Style:                          block.Style,
			PositionMediaButtons:           block.PositionMediaButtons,
			Links:                          block.Links,
			Disable:                        block.Disable,
			ApplyFor:                       block.ApplyFor,
			Trademark:                      block.Trademark,
			ProductLabelsAndBadges:         block.ProductLabelsAndBadges,
			ProductTabTitle:                block.ProductTabTitle,
			ProductTabHeadingSelector:      block.ProductTabHeadingSelector,
			ProductTabDisplay:              block.ProductTabDisplay,
			ProductTabHorizontalAutoSwitch: block.ProductTabHorizontalAutoSwitch,
			ProductTabAccordionStyle:       block.ProductTabAccordionStyle,
			Appearance:                     block.Appearance,
			ScrollingTextBanner:            block.ScrollingTextBanner,
			ScrollingSpeed:                 block.ScrollingSpeed,
			PauseOnMouseover:               block.PauseOnMouseover,
			Messages:                       block.Messages,
			// spending goal
			Goal:                  block.Goal,
			Discount:              block.Discount,
			MessageSpendingGoal:   block.MessageSpendingGoal,
			PlacementSpendingGoal: block.PlacementSpendingGoal,
			// order limit
			OrderLimitSetting: block.OrderLimitSetting,
			// product limit
			ProductLimitSetting: block.ProductLimitSetting,
			// feature icon
			FeatureIcon: block.FeatureIcon,
			// comparison slider
			ComparisonSlider: block.ComparisonSlider,
		})
	}
	return c.JSON(http.StatusOK, prodBlocks)
}

func (api *Api) UpdateProductBlock(c echo.Context) error {
	id := c.Param("blockId")
	var block model.ProductBlockUpdate
	if err := c.Bind(&block); err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	block.UpdateAt = time.Now()
	block.UpdateBy = c.Get("shop").(string)
	shop := c.Get("shop").(string)
	currentBlock, err := Db.FindBlockByCode(shop, block.Code)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	currentDiscount := currentBlock.Discount
	updatedCount, err := Db.UpdateProductBlock(id, block)
	if err != nil {
		return c.String(http.StatusInternalServerError, err.Error())
	}
	// if block code is spending_goal, update the discount
	if block.Code == "spending_goal_tracker" && block.IsActive {
		go func() {
			store, _ := api.Repo.Store.FindOne(shop)
			discount := model.SpendingGoalDiscount{
				MinimumPurchaseAmount: int64(block.Goal),
				Type:                  block.Discount.Type,
				Value:                 block.Discount.Value,
				CombineWithOther:      block.CombineWithOtherDiscount,
			}
			shopify := shopify.New(shop, store.AccessToken, api.Server.Config.ShopifyVersion)
			// deactivate old discount
			if err == nil && currentDiscount.ShopifyID != "" {
				err := shopify.DeactivateSpendingGoalDiscount(context.Background(), currentDiscount.ShopifyID)
				if err != nil {
					log.Logger.Error(fmt.Sprintf("failed to deactivate spending goal discount: %s, error: %s", currentDiscount.ShopifyID, err.Error()))
				}
			}
			shopifyID, discountCode, err := shopify.CreateSpendingGoalDiscount(context.Background(), &discount)
			if err != nil {
				log.Logger.Error(fmt.Sprintf("failed to create spending goal discount: %s, error: %s", id, err.Error()))
			}
			if err == nil {
				discount.Code = discountCode
				discount.ShopifyID = shopifyID
				block.Discount = discount
				Db.UpdateProductBlock(id, block)
			}
		}()
	}
	return c.JSON(http.StatusOK, struct {
		UpdatedCount int64 `json:"updated_count"`
	}{UpdatedCount: updatedCount})
}

func (api *Api) GetAppBlockDeedLink(c echo.Context) error {
	appBlockID := api.Server.Config.AppBlockID
	code := c.Param("blockCode")
	host := c.Get("host").(string)
	type DeepLink struct {
		Url string `json:"url"`
	}
	decodedHost, _ := base64.RawStdEncoding.DecodeString(host)
	dl := DeepLink{
		Url: fmt.Sprintf("https://%s/admin/themes/current/editor?template=product&addAppBlockId=%s/%s&target=mainSection", string(decodedHost), appBlockID, code),
	}
	if code == "trustz" {
		dl = DeepLink{
			Url: fmt.Sprintf("https://%s/admin/themes/current/editor?context=apps&template=product&activateAppId=%s/%s", string(decodedHost), appBlockID, code),
		}
	}
	return c.JSON(http.StatusOK, dl)
}

func (api *Api) VerifyAppBlockInThemeHandler(c echo.Context) error {
	appBlockID := api.Server.Config.AppBlockID
	shop := c.Get("shop").(string)
	block := c.QueryParam("block")
	store, _ := api.Repo.Store.FindOne(shop)
	client := goshopify.NewClient(goshopify.App{}, store.Shop, store.AccessToken)
	themes, err := client.Theme.List(nil)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err)
	}
	var themeMain goshopify.Theme
	for _, theme := range themes {
		if theme.Role == "main" {
			themeMain = theme
			break
		}
	}

	// Create a channel to receive results from all goroutines
	resultChan := make(chan bool)

	// Run the asset checking in a goroutine
	go func() {
		// get assets of theme
		assets, err := client.Asset.List(themeMain.ID, nil)
		if err != nil {
			resultChan <- false
			return
		}

		// Create a channel to collect results from individual asset checks
		assetResultChan := make(chan bool)
		activeGoroutines := 0

		for _, item := range assets {
			if (item.Key == "templates/product.json" || strings.Contains(item.Key, "templates/product.")) && !strings.Contains(item.Key, ".liquid") {
				activeGoroutines++
				// Run each asset check in a separate goroutine
				go func(key string) {
					asset, err := client.Asset.Get(themeMain.ID, key)
					log.Logger.Info(fmt.Sprintf("check templates product %s", key))
					if err == nil {
						if strings.Contains(asset.Value, fmt.Sprintf("blocks\\/%s", block)) && strings.Contains(asset.Value, appBlockID) {
							assetResultChan <- true
							return
						}
					}
					assetResultChan <- false
				}(item.Key)
			}
		}

		// Collect results from all goroutines
		appBlockExisted := false
		for i := 0; i < activeGoroutines; i++ {
			if <-assetResultChan {
				appBlockExisted = true
				break
			}
		}
		resultChan <- appBlockExisted
	}()

	// Wait for the final result
	appBlockExisted := <-resultChan

	if appBlockExisted {
		return c.NoContent(http.StatusOK)
	}

	return c.NoContent(http.StatusNoContent)
}

func (api *Api) VerifyAppEmbed(c echo.Context) error {
	appBlockID := api.Server.Config.AppBlockID
	type Block struct {
		Type     string `json:"type"`
		Disabled bool   `json:"disabled"`
	}
	type ConfigSettingData struct {
		Current map[string]map[string]Block `json:"current"`
	}
	shop := c.Get("shop").(string)
	store, _ := api.Repo.Store.FindOne(shop)
	client := goshopify.NewClient(goshopify.App{}, store.Shop, store.AccessToken, goshopify.WithVersion("2024-01"))
	themes, err := client.Theme.List(nil)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err)
	}
	var themeMain goshopify.Theme
	for _, theme := range themes {
		if theme.Role == "main" {
			themeMain = theme
			break
		}
	}
	log.Logger.Info("verify app_embedded status", "shop", shop)
	// get assets of theme
	assets, err := client.Asset.List(themeMain.ID, nil)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err)
	}
	appEmbedExisted := false
	for _, item := range assets {
		if item.Key == "config/settings_data.json" {
			asset, err := client.Asset.Get(themeMain.ID, item.Key)
			if err == nil {
				var settingData ConfigSettingData
				json.Unmarshal([]byte(asset.Value), &settingData)
				blocks, ok := settingData.Current["blocks"]
				if ok {
					for _, block := range blocks {
						log.Logger.Info("block info", "block", block.Type, "shop", shop)
						if strings.Contains(block.Type, appBlockID) && !block.Disabled {
							appEmbedExisted = true
						}
					}
				}
			}
		}
	}
	go func(appEmbedExisted bool) {
		// update app embedded status
		update := primitive.M{"$set": primitive.M{"app_embed": appEmbedExisted}}
		result, err := api.Repo.Store.Collection.UpdateOne(context.TODO(), primitive.M{"shop": store.Shop}, update)
		if err != nil {
			log.Logger.Error("update app_embed status error", "error", err.Error())
		} else {
			log.Logger.Info("update app_embed status successful", "app_embed", appEmbedExisted, "shop", shop, "result", result.MatchedCount)
		}
	}(appEmbedExisted)

	if appEmbedExisted {
		return c.NoContent(http.StatusOK)
	}

	return c.NoContent(http.StatusNoContent)
}

func (api *Api) SizeChartCategoriesHandler(c echo.Context) error {
	cats, err := Db.GetSizeChartCategories(context.Background())
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err)
	}
	return c.JSON(http.StatusOK, cats)
}

type Product struct {
	Handle        string `json:"handle"`
	Title         string `json:"title"`
	FeaturedImage struct {
		Url string `json:"url"`
	} `json:"featuredImage"`
	TotalInventory   int64  `json:"totalInventory"`
	LegacyResourceId string `json:"legacyResourceId"`
}

type ProductQuery struct {
	Products struct {
		Edges []struct {
			Cursor string  `json:"cursor"`
			Node   Product `json:"node"`
		} `json:"edges"`
		PageInfo struct {
			HasNextPage     bool   `json:"hasNextPage"`
			EndCursor       string `json:"endCursor"`
			StartCursor     string `json:"startCursor"`
			HasPreviousPage bool   `json:"hasPreviousPage"`
		} `json:"pageInfo"`
	} `json:"products" graphql:"products(query: $query, first: $first, after: $after)"`
}

func (api *Api) ProductSearchHandler(c echo.Context) error {
	ctx := context.Background()
	shop := c.Get("shop").(string)
	store, err := api.Repo.Store.FindOne(shop)
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	title := c.QueryParam("title")
	nextPage := c.QueryParam("nextPage")
	tag := c.QueryParam("tag")
	tags := strings.Split(tag, ",")
	client := shopify.New(store.Shop, store.AccessToken, api.Server.Config.ShopifyVersion)
	products, pageInfo, err := client.ProductList(ctx, title, nextPage, tags)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err)
	}
	return c.JSON(http.StatusOK, server.Response{
		Data: products,
		Metadata: struct {
			PageInfo shopify.PageInfo `json:"pageInfo"`
		}{PageInfo: pageInfo},
		Status: http.StatusOK,
	})
}

func (api *Api) CollectionListHandler(c echo.Context) error {
	ctx := context.Background()
	shop := c.Get("shop").(string)
	title := c.QueryParam("title")
	nextPage := c.QueryParam("nextPage")
	store, _ := api.Repo.Store.FindOne(shop)
	client := shopify.New(store.Shop, store.AccessToken, api.Server.Config.ShopifyVersion)
	collections, pageInfo, err := client.CollectionList(ctx, title, nextPage)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err)
	}
	return c.JSON(http.StatusOK, struct {
		Collections []shopify.CollectionEdge `json:"collections"`
		PageInfo    shopify.PageInfo         `json:"pageInfo"`
	}{Collections: *collections, PageInfo: pageInfo})
}

func (api *Api) ProductTagListHandler(c echo.Context) error {
	ctx := context.Background()
	shop := c.Get("shop").(string)
	store, _ := api.Repo.Store.FindOne(shop)
	client := shopify.New(store.Shop, store.AccessToken, api.Server.Config.ShopifyVersion)
	tags, err := client.ProductTags(ctx)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err)
	}
	return c.JSON(http.StatusOK, tags)
}

func (api *Api) LabelCategoriesHandler(c echo.Context) error {
	cats, err := Db.GetProductLabelCategories(context.Background())
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err)
	}
	return c.JSON(http.StatusOK, cats)
}

func (api *Api) ListProductLabelHandler(c echo.Context) error {
	categories := c.QueryParams()["category[]"]
	title := c.QueryParam("title")
	page := c.QueryParam("page")
	pageSize := 100
	pageNum, _ := strconv.ParseInt(page, 10, 64)
	if pageNum == 0 {
		pageNum = 1
	}
	labels, total, hasNext, nextPage, err := Db.GetProductLabels(context.Background(), title, categories, pageNum, int64(pageSize))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err)
	}
	return c.JSON(http.StatusOK, server.Response{
		Data: labels,
		Metadata: struct {
			HasNext  bool  `json:"hasNext"`
			Total    int64 `json:"total"`
			NextPage int64 `json:"nextPage"`
			Page     int64 `json:"page"`
			PageSize int64 `json:"pageSize"`
		}{
			HasNext:  hasNext,
			Page:     pageNum,
			PageSize: int64(pageSize),
			Total:    total,
			NextPage: nextPage,
		},
		Status: http.StatusOK,
	})
}

func (api *Api) initProductBlockRoute() {
	api.AdminRoute.ProductBlock.GET("/collections", api.CollectionListHandler)
	api.AdminRoute.ProductBlock.GET("/product_tags", api.ProductTagListHandler)
	api.AdminRoute.ProductBlock.GET("/products/search", api.ProductSearchHandler)
	api.AdminRoute.ProductBlock.GET("/size_chart_categories", api.SizeChartCategoriesHandler)
	api.AdminRoute.ProductBlock.GET("/label_categories", api.LabelCategoriesHandler)
	api.AdminRoute.ProductBlock.GET("/labels", api.ListProductLabelHandler)
	api.AdminRoute.ProductBlock.GET("", api.ListProductBlock)
	api.AdminRoute.ProductBlock.PATCH("/:blockId", api.UpdateProductBlock)
	api.AdminRoute.ProductBlock.GET("/:blockCode/deep_link", api.GetAppBlockDeedLink)
	api.AdminRoute.ProductBlock.GET("/verify_app_block", api.VerifyAppBlockInThemeHandler)
	api.AdminRoute.ProductBlock.GET("/verify_app_embed", api.VerifyAppEmbed)
	api.StorefrontRoute.ProductBlock.GET("", api.ListProductBlockStorefront)
}

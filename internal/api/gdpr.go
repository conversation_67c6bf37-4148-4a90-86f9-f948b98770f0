package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
)

func (api *Api) GdprMandatory(c echo.Context) error {
	return c.NoContent(http.StatusOK)
}

func (api *Api) initGdpr() {
	api.WebhookRoute.Gdpr.POST("/customer", api.GdprMandatory).Name = "gdpr-customer"
	api.WebhookRoute.Gdpr.POST("/customer/erasure", api.GdprMandatory).Name = "gdpr-customer-erasure"
	api.WebhookRoute.Gdpr.POST("/shop/erasure", api.GdprMandatory).Name = "gdpr-shop-erasure"
}

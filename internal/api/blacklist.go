package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
)

func (api *Api) BlackListHandler(c echo.Context) error {
	code := c.Query<PERSON>aram("code")
	bl, err := Db.FindBlackList(code)
	if err != nil {
		return c.String(http.StatusBadRequest, err.Error())
	}
	return c.<PERSON>(http.StatusOK, bl)
}

func (api *Api) initBlackListRoute() {
	api.AdminRoute.BlackList.GET("", api.BlackListHandler)
}

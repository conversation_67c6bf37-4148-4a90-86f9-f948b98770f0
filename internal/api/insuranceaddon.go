package api

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"

	"github.com/jangnh/amote/internal/model"
	"github.com/jangnh/amote/internal/shopify"
	"github.com/labstack/echo/v4"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var shopifyVersion = "2025-04"

func (api *Api) initInsuranceAddonRoute() {
	api.Server.Echo.POST("/internal/create-default-insurance-addon", api.CreateDefaultInsuranceAddon)
	api.AdminRoute.InsuranceAddon.GET("", api.GetInsuranceAddon)
	api.AdminRoute.InsuranceAddon.PUT("/:id", api.UpdateInsuranceAddon)
	api.StorefrontRoute.InsuranceAddon.GET("", api.GetInsuranceAddonStorefront)
}

func (api *Api) GetInsuranceAddon(c echo.Context) error {
	shop := c.Get("shop").(string)
	insuranceAddon, err := Db.FindInsuranceAddon(shop, "")
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Data:   nil,
			Error:  err.Error(),
		})
	}
	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
		Data:   insuranceAddon,
		Error:  "",
	})
}

func (api *Api) GetInsuranceAddonStorefront(c echo.Context) error {
	shop := c.QueryParam("shop")
	insuranceAddon, err := Db.FindInsuranceAddon(shop, "")
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Data:   nil,
			Error:  err.Error(),
		})
	}
	insuranceAddon.Default = nil
	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
		Data:   insuranceAddon,
		Error:  "",
	})
}

func (api *Api) UpdateInsuranceAddon(c echo.Context) error {
	shop := c.Get("shop").(string)
	id := c.Param("id")
	insuranceAddon, err := Db.FindInsuranceAddon(shop, id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Data:   nil,
			Error:  err.Error(),
		})
	}

	var update model.InsuranceAddonUpdate
	if err := c.Bind(&update); err != nil {
		return c.JSON(http.StatusBadRequest, ApiResponse{
			Status: false,
			Data:   nil,
			Error:  err.Error(),
		})
	}
	// create new id for each item
	for i, item := range update.Items {
		if item.ID.IsZero() {
			update.Items[i].ID = primitive.NewObjectID()
		}
	}

	// keep product object for update
	for i, item := range update.Items {
		for _, insuranceAddonItem := range insuranceAddon.Items {
			if item.ID.String() == insuranceAddonItem.ID.String() {
				update.Items[i].Product = insuranceAddonItem.Product
			}
		}
	}

	updatedInsuranceAddon, err := Db.UpdateInsuranceAddon(shop, id, update)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Data:   nil,
			Error:  err.Error(),
		})
	}

	// handle create insurance addon product if not exist
	if updatedInsuranceAddon.IsActive {
		store, err := api.Repo.Store.FindOne(shop)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, ApiResponse{
				Status: false,
				Data:   nil,
				Error:  err.Error(),
			})
		}
		shopifyClient := shopify.New(store.Shop, store.AccessToken, shopifyVersion)
		ctx := context.Background()
		items := updatedInsuranceAddon.Items
		for _, item := range items {
			if item.Product == nil {
				// create product
				product, err := shopifyClient.CreateInsuranceAddonProduct(ctx, shopify.InsuranceAddonProductInput{
					Title:           item.Title,
					Price:           item.Price,
					DescriptionHTML: item.Description,
					Status:          "ACTIVE",
				}, shopify.CreateMediaInput{
					Alt:              item.Title,
					MediaContentType: "IMAGE",
					OriginalSource:   item.Image,
				})
				if err != nil {
					slog.Error(fmt.Sprintf("failed to create insurance addon product: %v", err))
					return c.JSON(http.StatusInternalServerError, ApiResponse{
						Status: false,
						Data:   nil,
						Error:  err.Error(),
					})
				}
				// serialize product
				p := model.Product{
					ID:     product.ProductCreate.Product.ID,
					Title:  product.ProductCreate.Product.Title,
					Handle: product.ProductCreate.Product.Handle,
					Status: product.ProductCreate.Product.Status,
				}
				variants := []model.Variant{}
				variantEdges := product.ProductCreate.Product.Variants.Edges
				for _, variantEdge := range variantEdges {
					variant := variantEdge.Node
					variants = append(variants, model.Variant{
						ID:    variant.ID,
						Price: variant.Price,
					})
				}
				p.Variants = variants
				p.Media = &model.ProductMedia{
					ID: product.ProductCreate.Product.Media.Edges[0].Node.ID,
				}
				// Update the item's product field with the new product information
				// Update the item in MongoDB
				filter := primitive.M{"shop": shop, "_id": insuranceAddon.ID, "items._id": item.ID}
				updateDoc := primitive.M{"$set": primitive.M{"items.$.product": p}}
				_, err = Db.InsuranceAddon.UpdateOne(context.Background(), filter, updateDoc)
				if err != nil {
					slog.Error(fmt.Sprintf("failed to update insurance addon item product: %v", err))
					return c.JSON(http.StatusInternalServerError, ApiResponse{
						Status: false,
						Data:   nil,
						Error:  err.Error(),
					})
				}
				err = shopifyClient.PublishProductToCurrentChannel(ctx, p.ID)
				if err != nil {
					slog.Error(fmt.Sprintf("failed to publish insurance addon item product: %v", err))
					return c.JSON(http.StatusInternalServerError, ApiResponse{
						Status: false,
						Data:   nil,
						Error:  err.Error(),
					})
				}
				slog.Info(fmt.Sprintf("published insurance addon item %s, product id %s", item.ID.String(), p.ID))
				slog.Info(fmt.Sprintf("created insurance addon item %s, product id %s", item.ID.String(), p.ID))
			} else if item.Product != nil && item.Product.Status == "ACTIVE" {
				// delete old media
				err = shopifyClient.DeleteInsuranceProductMedia(ctx, item.Product.Media.ID, item.Product.ID)
				if err != nil {
					slog.Error(fmt.Sprintf("failed to delete insurance addon item product media: %v", err))
					return c.JSON(http.StatusInternalServerError, ApiResponse{
						Status: false,
						Data:   nil,
						Error:  err.Error(),
					})
				}
				// update product
				productUpdate, err := shopifyClient.UpdateInsuranceAddonProduct(ctx, shopify.InsuranceAddonProductInput{
					ID:              item.Product.ID,
					Title:           item.Title,
					Price:           item.Price,
					DescriptionHTML: item.Description,
				}, shopify.CreateMediaInput{
					Alt:              item.Title,
					MediaContentType: "IMAGE",
					OriginalSource:   item.Image,
				})
				if err != nil {
					slog.Error(fmt.Sprintf("failed to update insurance addon product: %v", err))
					return c.JSON(http.StatusInternalServerError, ApiResponse{
						Status: false,
						Data:   nil,
						Error:  err.Error(),
					})
				}
				// serialize product
				p := model.Product{
					ID:     productUpdate.ProductUpdate.Product.ID,
					Title:  productUpdate.ProductUpdate.Product.Title,
					Handle: productUpdate.ProductUpdate.Product.Handle,
					Status: productUpdate.ProductUpdate.Product.Status,
				}
				variants := []model.Variant{}
				variantEdges := productUpdate.ProductUpdate.Product.Variants.Edges
				for _, variantEdge := range variantEdges {
					variant := variantEdge.Node
					variants = append(variants, model.Variant{
						ID:    variant.ID,
						Price: variant.Price,
					})
				}
				p.Variants = variants
				p.Media = &model.ProductMedia{
					ID: productUpdate.ProductUpdate.Product.Media.Edges[0].Node.ID,
				}
				// Update the item's product field with the updated product information
				item.Product = &p
				// Update the item in the update.Items slice
				// Update the item in MongoDB
				filter := primitive.M{"shop": shop, "_id": insuranceAddon.ID, "items._id": item.ID}
				updateDoc := primitive.M{"$set": primitive.M{"items.$.product": p}}
				_, err = Db.InsuranceAddon.UpdateOne(context.Background(), filter, updateDoc)
				if err != nil {
					slog.Error(fmt.Sprintf("failed to update insurance addon item product: %v", err))
					return c.JSON(http.StatusInternalServerError, ApiResponse{
						Status: false,
						Data:   nil,
						Error:  err.Error(),
					})
				}
				slog.Info(fmt.Sprintf("updated insurance addon item %s, product id %s", item.ID.String(), p.ID))
			}
		}
	}
	// refresh insurance addon
	insuranceAddon, err = Db.FindInsuranceAddon(shop, id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Data:   nil,
			Error:  err.Error(),
		})
	}
	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
		Data:   insuranceAddon,
		Error:  "",
	})
}

func (api *Api) CreateDefaultInsuranceAddon(c echo.Context) error {
	type CreateDefaultInsuranceAddonRequest struct {
		Shop []string `json:"shop"`
	}
	var request CreateDefaultInsuranceAddonRequest
	if err := c.Bind(&request); err != nil {
		return c.JSON(http.StatusBadRequest, ApiResponse{
			Status: false,
			Data:   nil,
			Error:  err.Error(),
		})
	}
	filter := primitive.M{}

	if len(request.Shop) > 0 {
		filter["shop"] = primitive.M{"$in": request.Shop}
	}
	ctx := context.Background()
	// Get total count for pagination
	total, err := Db.Shop.CountDocuments(ctx, filter)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, ApiResponse{
			Status: false,
			Data:   nil,
			Error:  err.Error(),
		})
	}

	go func() {
		// Initialize variables for batch processing
		batchSize := int32(100)
		ctx := context.Background()
		opts := options.Find().SetBatchSize(batchSize).SetLimit(int64(batchSize))
		// Process in batches
		for skip := int64(0); skip < total; skip += int64(batchSize) {
			var insuranceAddons []interface{}
			opts.SetSkip(skip)
			cur, err := Db.Shop.Find(ctx, filter, opts)
			if err != nil {
				slog.Error(fmt.Sprintf("failed to find documents: %v", err))
				continue
			}

			var batch []model.Store
			if err = cur.All(ctx, &batch); err != nil {
				slog.Error(fmt.Sprintf("failed to decode batch: %v", err))
				continue
			}
			// build insurance addon for batch insert
			for _, store := range batch {
				insuranceAddonDefault := model.InsuranceAddonDefault
				// Set _id for insurance addon
				insuranceAddonDefault.ID = primitive.NewObjectID()
				insuranceAddonDefault.Shop = store.Shop
				// handle insurance addon items
				insuranceAddonDefault.Items = make([]model.InsuranceAddonItem, len(model.InsuranceAddonDefault.Items))
				copy(insuranceAddonDefault.Items, model.InsuranceAddonDefault.Items)
				// Set _id for each item
				for i := range insuranceAddonDefault.Items {
					insuranceAddonDefault.Items[i].ID = primitive.NewObjectID()
				}
				insuranceAddons = append(insuranceAddons, insuranceAddonDefault)
			}
			insertBatch, err := Db.InsuranceAddon.InsertMany(ctx, insuranceAddons)
			if err != nil {
				slog.Error(fmt.Sprintf("failed to insert insurance addon batch %d: %v", skip, err))
				continue
			}
			insertedIDs := insertBatch.InsertedIDs
			slog.Info(fmt.Sprintf("inserted %d insurance addons, batch %d", len(insertedIDs), skip))
		}
	}()

	return c.JSON(http.StatusOK, ApiResponse{
		Status: true,
		Data:   fmt.Sprintf("processing %d shops", total),
		Error:  "",
	})
}

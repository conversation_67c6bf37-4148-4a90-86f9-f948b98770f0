package app

import (
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"time"

	"github.com/jangnh/amote/internal/config"
	"github.com/jangnh/amote/internal/repositories"
	_ "github.com/joho/godotenv/autoload"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

type Server struct {
	Echo   *echo.Echo
	DB     *repositories.DB
	Config *config.Config
	logger *slog.Logger
}

func NewServer() *Server {
	e := echo.New()
	c := config.Init()
	db := repositories.NewDB(c.<PERSON>, c.DB<PERSON>ame)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelInfo}))
	slog.SetDefault(logger)
	srv := &Server{
		Echo:   e,
		Config: c,
		DB:     db,
		logger: logger,
	}
	return srv
}

func (srv *Server) Start() {
	e := srv.Echo
	e.Use(middleware.Recover())
	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins: []string{"*"},
		AllowHeaders: []string{"*"},
	}))

	s := &http.Server{
		Addr:         srv.Config.ListenAddress,
		WriteTimeout: time.Second * 15,
		ReadTimeout:  time.Second * 15,
		IdleTimeout:  time.Second * 15,
		Handler:      e,
	}

	defer srv.DB.Shutdown()

	slog.Info(fmt.Sprintf("start server at address: %s", srv.Config.ListenAddress))
	if err := s.ListenAndServe(); err != http.ErrServerClosed {
		panic(err.Error())
	}
}

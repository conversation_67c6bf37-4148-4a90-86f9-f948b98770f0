package server

import (
	"github.com/jangnh/amote/internal/api"
	"github.com/jangnh/amote/internal/app"
	"github.com/spf13/cobra"
)

// ServerCmd represents the server command
var ServerCmd = &cobra.Command{
	Use:   "server",
	Short: "Start the API server",
	Long:  `Start the TrustZ API server with all configured routes and middleware.`,
	Run: func(cmd *cobra.Command, args []string) {
		runServer()
	},
}

func runServer() {
	srv := app.NewServer()
	api.Init(srv)
	srv.Start()
}
